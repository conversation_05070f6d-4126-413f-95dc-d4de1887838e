{"manifest_version": 3, "name": "CubAI", "version": "1.0", "description": "A React-based Chrome extension sidebar.", "permissions": ["sidebarAction", "sidePanel", "storage", "activeTab", "scripting", "tabs", "contextMenus", "webRequest", "tabCapture"], "host_permissions": ["<all_urls>"], "side_panel": {"default_path": "index.html"}, "background": {"service_worker": "background.js", "type": "module"}, "action": {"default_title": "Open CubAI"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content_script.js"], "run_at": "document_idle"}], "web_accessible_resources": [{"resources": ["content_script.js"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "browser_specific_settings": {"gecko": {"strict_min_version": "109.0"}}, "icons": {"16": "cubai2.png", "48": "cubai2.png", "128": "cubai2.png"}}