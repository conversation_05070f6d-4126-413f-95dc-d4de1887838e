@tailwind base;
@tailwind components;
@tailwind utilities;

/* General Body Styles */
body {
  height: 100vh;
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #000; /* Black background */
  color: #e0e0e0; /* Light text */
  overflow-x: hidden; /* Disable horizontal scrolling */
  overflow-y: hidden; /* Disable vertical scrolling */
}
.text-type {
  display: inline-block;
  white-space: pre-wrap;
}

.text-type__cursor {
  margin-left: 0.25rem;
  display: inline-block;
  opacity: 1;
}

.text-type__cursor--hidden {
  display: none;
}
.orb-background {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  z-index: -1;
}

/* App Container */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
  background-color: transparent; /* Make app container transparent */
  position: relative;
  z-index: 1;
}

/* Header - minimal container only (visual styling handled by ShinyText) */
.header {
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #917e64;
  backdrop-filter: blur(10px);
}

/* Main Content & Scrolling */
.main-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 16px;
  scroll-behavior: smooth;
  /* Ensure content never sits behind the floating input bar */
  padding-bottom: 120px; /* space for the curved bar + margin */
}

/* Custom Scrollbar */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

/* Chat Area */
.chat-area {
  display: flex;
  flex-direction: column;
  gap: 16px;
  /* Guarantee the very last message can scroll above the input bar */
  padding-bottom: 40px;
}

/* Message Styling & Animations */
@keyframes fadeInSlideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px; /* Smaller font size */
  line-height: 1.6; /* Adjusted for readability */
  max-width: 85%;
  animation: fadeInSlideUp 0.5s ease-out forwards;
  word-wrap: break-word;
}

.user-message {
  background: rgba(255, 255, 255, 0.05); /* More translucent */
  color: #f0f0f0; /* Lighter text for contrast */
  align-self: flex-end;
  border-bottom-right-radius: 4px;
  backdrop-filter: blur(5px);
}

.ai-message {
  background: rgba(255, 255, 255, 0.05); /* More translucent */
  color: #f0f0f0; /* Lighter text for contrast */
  align-self: flex-start;
  border-bottom-left-radius: 4px;
  backdrop-filter: blur(5px);
}

.ai-message img {
    border-radius: 12px;
    margin-top: 8px;
}

/* Input Area */
/* Footer input area removed in UI; keep class for backward compatibility if present elsewhere */
.input-area {
  display: none;
}

.photo-icon-button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-right: 12px;
  transition: transform 0.2s ease;
}

.photo-icon-button:hover {
    transform: scale(1.1);
}

.photo-icon-button svg {
  width: 26px;
  height: 26px;
  fill: #43cea2;
}

.text-input {
  flex-grow: 1;
  background-color: #3a3a3c;
  border: 1px solid transparent;
  border-radius: 20px;
  padding: 10px 16px;
  font-size: 14px; /* Smaller font size */
  color: #e0e0e0;
  outline: none;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Chat-like input surface and send button to match screenshot */
.input-surface {
  /* styles applied inline in JSX for backdrop + borders; keep class for future overrides */
}

.chatlike-input {
  resize: none;
  overflow-y: auto;
  max-height: 40vh;
  line-height: 1.5;
}

.chatlike-input::placeholder {
  color: #9ca3af;
}

.send-fab {
  width: 36px;
  height: 36px;
  border-radius: 9999px;
  border: 1px solid rgba(255,255,255,0.2);
  background: transparent;
  color: #9ca3af;
  display: grid;
  place-items: center;
  cursor: pointer;
  transition: transform .15s ease, background-color .2s ease, color .2s ease, border-color .2s ease;
}

.send-fab:hover:not(:disabled) {
  transform: translateY(-1px);
  background: rgba(255,255,255,0.06);
  color: #e5e7eb;
  border-color: rgba(255,255,255,0.28);
}

.send-fab:disabled {
  opacity: .5;
  cursor: not-allowed;
}

/* Curved rectangular visual pill */
.text-input.curved {
  height: 48px;
  border-radius: 9999px; /* fully rounded ends */
  background: rgba(255,255,255,0.06);
  border: 1px solid rgba(255,255,255,0.14);
  box-shadow:
    0 2px 10px rgba(0,0,0,0.25),
    inset 0 1px 0 rgba(255,255,255,0.06),
    inset 0 0 12px rgba(255,255,255,0.03);
}

.text-input::placeholder {
  color: #8e8e93;
}

/* Opaque variant for curved input */
.text-input.curved.opaque {
  background: rgba(255,255,255,0.12);
  border: 1px solid rgba(255,255,255,0.2);
  color: #eaeaea;
}

.floating-input-bar .icon-button.photo svg {
  width: 26px;
  height: 26px;
  fill: #9ca3af;
}

.send-button.curved {
  border-radius: 9999px;
  padding: 10px 18px;
}

.text-input:focus {
  border-color: #43cea2;
  box-shadow: 0 0 0 3px rgba(67, 206, 162, 0.3);
}

.text-input:disabled {
    opacity: 0.5;
}

/* Loading and Error states */
.loading-message, .error-message {
    text-align: center;
    color: #8e8e93;
    font-style: italic;
}

/* Table Styling for ReactMarkdown */
.message table {
  border-collapse: collapse;
  width: 100%;
  margin: 12px 0;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message th,
.message td {
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 8px 12px;
  text-align: left;
  vertical-align: top;
}

.message th {
  background-color: rgba(255, 255, 255, 0.1);
  font-weight: 600;
  color: #f0f0f0;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.message td {
  color: #e0e0e0;
}

.message tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.02);
}

.message tr:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

/* Responsive table styling */
@media (max-width: 768px) {
  .message table {
    font-size: 12px;
  }

  .message th,
  .message td {
    padding: 6px 8px;
  }
}

/* LaTeX Math Styling */
.message .katex {
  font-size: 1.1em;
}

.message .katex-display {
  margin: 16px 0;
  text-align: center;
}

.message .katex-display .katex {
  font-size: 1.2em;
}

/* Floating curved input bar spacing and stacking */
.floating-input-bar {
  pointer-events: none;         /* allow clicks to pass except inside children we enable */
}
.floating-input-bar .curved-input-wrapper {
  pointer-events: auto;
}
.input-wrapper {
  flex-grow: 1;
  display: flex;
  align-items: center;
  position: relative;
  margin-right: 12px;
}

.thumbnail-container {
  position: relative;
  margin-right: 8px;
}

.thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
}

.remove-thumbnail-button {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #333;
  color: white;
  border: 1px solid white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
}

.button-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  transition: transform 0.2s ease;
}

.icon-button:hover {
    transform: scale(1.1);
}

.icon-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.icon-button svg {
  width: 26px;
  height: 26px;
  fill: #9ca3af; /* gray */
}

.send-button {
  background-color: #43cea2;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.send-button:hover {
  background-color: #36b58f;
}

.send-button:disabled {
  background-color: #555;
  color: #999;
  cursor: not-allowed;
}